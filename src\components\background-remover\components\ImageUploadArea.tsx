import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from '@/components/ui/Dialog';
import { Input } from '@/components/ui/Input';
import { VisuallyHidden } from '@/components/ui/VisuallyHidden';
import { SAMPLE_IMAGES } from '@/config/constants';
import { getPasteShortcut } from '@/lib/device';
import type { DropzoneRootProps } from 'react-dropzone';
import { useTranslations } from 'next-intl';

interface ImageUploadAreaProps {
  getRootProps: () => DropzoneRootProps;
  open: () => void;
  isDragActive: boolean;
  handleLoadFromUrl: (url: string) => Promise<void>;
  handleLoadSampleImage: (
    url: string,
    name: string,
    processedUrl?: string
  ) => Promise<void>;
  imagesCount: number;
}

export const ImageUploadArea = ({
  getRootProps,
  open,
  isDragActive,
  handleLoadFromUrl,
  handleLoadSampleImage,
  imagesCount,
}: ImageUploadAreaProps) => {
  const singleImage = useTranslations('singleImage');
  const common = useTranslations('common');

  const [isUrlDialogOpen, setIsUrlDialogOpen] = useState(false);
  const [inputUrl, setInputUrl] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);

  const handleUrlSubmit = async () => {
    if (!inputUrl.trim()) return;

    setIsLoadingUrl(true);
    try {
      await handleLoadFromUrl(inputUrl);
      setIsUrlDialogOpen(false);
      setInputUrl('');
    } finally {
      setIsLoadingUrl(false);
    }
  };

  return (
    <div className='flex flex-col items-center justify-center w-full max-w-5xl mx-auto pb-16'>
      {/* 标题 */}
      <h1 className='text-[32px] font-bold text-text-primary mb-10 leading-tight'>
        {singleImage('initial.uploadImageToRemove')}
      </h1>

      {/* 主要上传区域 */}
      <div className='bg-white w-[996px] rounded-3xl shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)] p-4 mb-16'>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-2xl h-92 pt-26 ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-[#d0d0d0]'
          }`}
        >
          <div className='text-center'>
            <p className='text-text-primary text-base mb-4'>
              {singleImage('initial.dragAndDrop')}
            </p>
            <Button
              size='lg'
              className='h-12 px-6 rounded-xl w-66 cursor-pointer'
              onClick={e => {
                e.stopPropagation();
                // 检查图片数量限制
                if (imagesCount >= 10) {
                  // 这里应该显示提示，但为了简化，我们先注释掉
                  // showTips('error', 'The number of images exceeds the limit, and a maximum of 10 images can be uploaded.', 4000);
                  return;
                }
                open();
              }}
            >
              <Image
                src='/apps/icons/add.svg'
                alt='add'
                width={24}
                height={24}
              />
              <span className='text-text-primary text-base font-medium'>
                {singleImage('initial.uploadImage')}
              </span>
            </Button>

            <p className='text-sm text-text-secondary mt-4'>
              {singleImage('initial.pasteImage').replace(
                '(Ctrl +V)',
                `(${getPasteShortcut()})`
              )}{' '}
              {/* TODO: 需要国际化 "or" 文案 */}
              or{' '}
              <Dialog open={isUrlDialogOpen} onOpenChange={setIsUrlDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant='link'
                    className='p-0 h-auto text-sm text-[#ffcc03] hover:text-[#ffdb4d]'
                    onClick={e => e.stopPropagation()}
                  >
                    URL
                  </Button>
                </DialogTrigger>
                <DialogContent className='w-[542px] bg-white rounded-2xl shadow-[0px_4px_8px_0px_rgba(19,19,20,0.5)] border-0 p-0'>
                  <VisuallyHidden>
                    <DialogTitle>
                      {singleImage('initial.pasteImageUrl')}
                    </DialogTitle>
                  </VisuallyHidden>
                  {/* 顶部关闭按钮区域 */}
                  <div className='h-10 relative rounded-t-[6px] w-full border-b border-[#e7e7e7]'></div>

                  {/* 主要内容区域 */}
                  <div className='px-8 py-6'>
                    <div className='flex flex-col gap-6'>
                      {/* 标题和输入框 */}
                      <div className='flex flex-col gap-3'>
                        <h3 className='text-[#121212] text-[16px] font-medium  leading-[1.5]'>
                          {singleImage('initial.pasteImageUrl')}
                        </h3>
                        <div className='relative'>
                          <Input
                            placeholder={singleImage(
                              'initial.pleaseInputImageUrl'
                            )}
                            value={inputUrl}
                            onChange={e => setInputUrl(e.target.value)}
                            onKeyDown={e => {
                              if (e.key === 'Enter') {
                                handleUrlSubmit();
                              }
                            }}
                            disabled={isLoadingUrl}
                            className='h-10 bg-[#f9fafb] border-[#e7e7e7] rounded-lg px-3 py-[9px] text-[14px]  placeholder:text-[#b8b8b8] focus:ring-2 focus:ring-[#ffcc03] focus:border-[#ffcc03]'
                          />
                        </div>
                      </div>

                      {/* 按钮区域 */}
                      <div className='flex justify-end gap-3'>
                        <Button
                          variant='outline'
                          onClick={() => {
                            setIsUrlDialogOpen(false);
                            setInputUrl('');
                          }}
                          disabled={isLoadingUrl}
                          className='h-10 w-24 rounded-lg border-[#e7e7e7] bg-white text-[#000000] text-[16px] font-medium  hover:bg-gray-50'
                        >
                          {common('cancel')}
                        </Button>
                        <Button
                          onClick={handleUrlSubmit}
                          disabled={!inputUrl.trim() || isLoadingUrl}
                          className='h-10 w-24 rounded-lg bg-[#ffcc03] text-[#000000] text-[16px] font-medium  hover:bg-[#ffcc03]/90 border-0'
                        >
                          {isLoadingUrl ? (
                            <svg
                              className='animate-spin h-4 w-4'
                              xmlns='http://www.w3.org/2000/svg'
                              fill='none'
                              viewBox='0 0 24 24'
                            >
                              <circle
                                className='opacity-25'
                                cx='12'
                                cy='12'
                                r='10'
                                stroke='currentColor'
                                strokeWidth='4'
                              ></circle>
                              <path
                                className='opacity-75'
                                fill='currentColor'
                                d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                              ></path>
                            </svg>
                          ) : (
                            common('confirm')
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </p>
            <div className='flex justify-center items-center gap-3 mt-18'>
              <p className='text-sm text-text-secondary'>
                {singleImage('initial.supportedFormats')}
              </p>
              <div className='flex items-center gap-2'>
                {['JPG', 'JPEG', 'PNG', 'WebP'].map(format => (
                  <div
                    key={format}
                    className='bg-gray-50 border border-[#e7e7e7] rounded px-3 py-1 text-xs text-text-primary'
                  >
                    {format}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 示例图片 */}
      <div className='w-full text-center'>
        <h3 className='text-base text-text-secondary mb-4'>
          {singleImage('initial.noImageTryThese')}
        </h3>
        <div className='flex justify-center gap-3'>
          {SAMPLE_IMAGES.map(sample => (
            <div
              key={sample.id}
              className='group cursor-pointer rounded-lg overflow-hidden'
              onClick={() =>
                handleLoadSampleImage(
                  sample.url,
                  sample.name,
                  sample.processedUrl
                )
              }
            >
              <div className='w-16 h-16 rounded-lg overflow-hidden bg-gray-300'>
                <Image
                  src={sample.url}
                  alt={sample.name}
                  className='w-full h-full object-cover transition-transform duration-300 group-hover:scale-110'
                  width={64}
                  height={64}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer text */}
      <p className='text-xs text-text-secondary mt-6'>
        {singleImage('initial.recaptchaNotice')}
      </p>
    </div>
  );
};
