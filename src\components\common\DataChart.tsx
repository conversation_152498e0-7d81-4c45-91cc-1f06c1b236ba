import React, { useMemo } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  ChartOptions,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { HistoryData } from '@/api/auth';
import CrosshairPlugin from 'chartjs-plugin-crosshair';
import Image from 'next/image';

// 注册插件
// 注意：注册顺序和渲染层级有一定关系，不可随意调动
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  CrosshairPlugin, // 注册纵轴线插件
  Tooltip,
  Filler
);

export const options: ChartOptions<'line'> = {
  responsive: true,
  // 核心交互配置
  interaction: {
    mode: 'index', // 按X轴索引触发所有数据点
    intersect: false, // 不要求精确悬停数据点
    axis: 'x', // 仅响应X轴方向的触摸
  },
  plugins: {
    legend: {
      display: false,
    },
    crosshair: {
      line: {
        color: 'rgba(255, 204, 3,.7)',
        width: 1,
        // dashPattern: [2, 2], // 虚线样式 // Set lower z-index to ensure line renders below tooltips
      },
    },
    tooltip: {
      enabled: true,
      mode: 'index', // 显示同一X位置的所有数据点
      intersect: false,
      backgroundColor: '#ffffff', // 白色背景
      bodyColor: '#000', // 文字颜色
      borderColor: 'rgba(0,0,0,0.1)',
      borderWidth: 1,
      padding: 12,
      titleMarginBottom: 0, // Remove extra space at tooltip top
      titleSpacing: 0, // Remove spacing after title
      titleFont: { size: 0 }, // Hide title completely
      displayColors: false, // 隐藏颜色标记点
      callbacks: {
        // 自定义标签内容
        // @ts-expect-error - 不必检查的类型
        label: (context: {
          chart: {
            data: {
              datasets: Array<{
                fill?: boolean;
                data: number[];
                context: HistoryData[];
                borderColor: string;
                backgroundColor: string;
                showLegend: boolean;
              }>;
            };
          };
          datasetIndex: number;
          dataIndex: number;
        }) => {
          const dataset = context.chart.data.datasets[context.datasetIndex];
          const contextData = dataset.context[context.dataIndex];
          return [
            `Date: ${contextData.date}`,
            `Time: ${contextData.time}`,
            `Amount: ${contextData.num}`,
            `Description: ${contextData.remark}`,
          ];
        },
      },
    },
  },
  scales: {
    x: {
      grid: {
        display: false, // 隐藏X轴网格线
        // drawBorder: false, // 可选：隐藏坐标轴线
      },
    },
    y: {
      grid: {
        display: false, // 隐藏Y轴网格线
        // drawBorder: false, // 可选：隐藏坐标轴线
      },
    },
  },
};

export default function DataChart({
  historyData,
}: {
  historyData: HistoryData[];
}) {
  const data = useMemo(() => {
    return {
      labels: historyData.map(item => item.date),
      datasets: [
        {
          fill: true,
          data: historyData.map(item => item.num),
          // Store additional data as context for tooltip usage
          context: historyData,
          borderColor: 'rgba(255, 204, 3)',
          backgroundColor: 'rgba(255, 204, 3, 0.2)',
          showLegend: false,
        },
      ],
    };
  }, [historyData]);
  return (
    <>
      {historyData.length ? (
        <Line options={options} data={data} />
      ) : (
        <div className='min-h-75 flex-col items-center justify-center'>
          <Image
            src='/apps/null_img.png'
            alt='null_img'
            width={150}
            height={120}
            className='mx-auto my-4'
          />
          <div className='text-center py-4'>No more data</div>
        </div>
      )}
    </>
  );
}
