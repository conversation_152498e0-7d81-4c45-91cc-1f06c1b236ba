'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/Tabs';
import { useImageStore } from '@/store/imageStore';
import { imageStorage } from '@/storage/indexeddbStorage';
import { useTranslations } from 'next-intl';

interface BackgroundSettings {
  type: 'color' | 'photos';
  color?: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string;
}
import Image from 'next/image';
import { PREDEFINED_COLORS } from '@/config/backgroundCategories';
import {
  BatchBackgroundImagePicker,
  type UploadedImage,
} from './BatchBackgroundImagePicker';
import { shouldDisableBatchApplyButton } from '../../../lib/buttonUtils';

interface BackgroundSettingsProps {
  onApply?: (settings: BackgroundSettings) => void;
  onBatchRemoveBackground?: (imageIds: string[]) => Promise<void>;
  images?: Array<{ id: string; status: string; processedUrl?: string | null }>;
  isProcessing?: boolean;
  uploadedImages?: UploadedImage[];
  onFileUpload?: (file: File) => void;
  // 新增：背景图片管理相关props
  uploadedBackgroundImages?: UploadedImage[];
  onAddBackgroundImage?: (file: File) => Promise<UploadedImage>;
}

export function BackgroundSettings({
  onApply,
  onBatchRemoveBackground,
  images = [],
  isProcessing = false,
  uploadedImages = [],
  onFileUpload,
  // 新增：背景图片管理相关props
  uploadedBackgroundImages = [],
  onAddBackgroundImage,
}: BackgroundSettingsProps) {
  const batchEditor = useTranslations('batchEditor');
  const common = useTranslations('common');

  const [activeTab, setActiveTab] = useState<'color' | 'photos'>('color');
  const [currentColor, setCurrentColor] = useState('transparent');

  const [isCustomColorActive, setIsCustomColorActive] = useState(() => {
    return !PREDEFINED_COLORS.includes(currentColor);
  });
  const [currentBackgroundImageUrl, setCurrentBackgroundImageUrl] = useState<
    string | undefined
  >(undefined);
  const [currentBackgroundImageId, setCurrentBackgroundImageId] = useState<
    string | undefined
  >(undefined);

  // 跟踪是否已经完成初始状态恢复
  const [hasRestoredInitialState, setHasRestoredInitialState] = useState(false);

  const colorInputRef = useRef<HTMLInputElement>(null);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!PREDEFINED_COLORS.includes(currentColor)) {
      setIsCustomColorActive(true);
    } else {
      setIsCustomColorActive(false);
    }
  }, [currentColor]);

  // 恢复背景图片选中状态
  useEffect(() => {
    // 如果已经完成初始状态恢复，不再执行
    if (hasRestoredInitialState) {
      return;
    }

    // 只有在背景图片加载完成且当前状态为初始状态时才恢复
    if (!uploadedBackgroundImages || uploadedBackgroundImages.length === 0) {
      return;
    }

    // 如果已经有选中的背景图片，不需要恢复
    if (currentBackgroundImageUrl !== undefined) {
      return;
    }

    // 检查当前图片是否有backgroundImageId需要恢复
    const firstImage = images[0];
    if (!firstImage) return;

    // 从store中获取图片的完整信息
    const imageFromStore = useImageStore.getState().images.get(firstImage.id);
    if (!imageFromStore?.backgroundImageId) {
      // 没有背景图片ID，标记为已恢复
      setHasRestoredInitialState(true);
      return;
    }

    // 查找对应的背景图片
    let matchedBackground: UploadedImage | undefined;

    if (imageFromStore.backgroundImageId.startsWith('preset-')) {
      // 预设背景图片，根据ID构造URL
      const presetName = imageFromStore.backgroundImageId.replace(
        'preset-',
        ''
      );
      const presetUrl = `/apps/images/background/${presetName}.png`;
      setCurrentBackgroundImageUrl(presetUrl);
      setCurrentBackgroundImageId(imageFromStore.backgroundImageId);
      setActiveTab('photos');
      setHasRestoredInitialState(true);
    } else {
      // 自定义上传的背景图片
      matchedBackground = uploadedBackgroundImages.find(
        bg => bg.id === imageFromStore.backgroundImageId
      );

      if (matchedBackground) {
        // 检查当前的URL是否与加载的背景图片URL匹配
        if (imageFromStore.backgroundImageUrl !== matchedBackground.url) {
          // 更新store中的backgroundImageUrl
          useImageStore.getState().updateImage(imageFromStore.id, {
            backgroundImageUrl: matchedBackground.url,
          });
        }

        setCurrentBackgroundImageUrl(matchedBackground.url);
        setCurrentBackgroundImageId(matchedBackground.id);
        setActiveTab('photos');
        setHasRestoredInitialState(true);
      } else {
        console.warn('未找到对应的背景图片:', imageFromStore.backgroundImageId);

        // 如果在当前列表中找不到，尝试从IndexedDB重新加载
        if (imageFromStore.backgroundImageUrl?.startsWith('blob:')) {
          // 异步重新加载背景图片
          (async () => {
            try {
              const loadResult = await imageStorage.loadBackgroundImage(
                imageFromStore.backgroundImageId!
              );
              if (loadResult) {
                // 更新store中的backgroundImageUrl
                useImageStore.getState().updateImage(imageFromStore.id, {
                  backgroundImageUrl: loadResult.url,
                });
                // 更新UI状态
                setCurrentBackgroundImageUrl(loadResult.url);
                setCurrentBackgroundImageId(imageFromStore.backgroundImageId);
                setActiveTab('photos');
              }
            } catch (error) {
              console.error(
                `从IndexedDB重新加载背景图片失败: ${imageFromStore.backgroundImageId}`,
                error
              );
            }
          })();
        }

        setHasRestoredInitialState(true);
      }
    }
  }, [
    uploadedBackgroundImages,
    currentBackgroundImageUrl,
    hasRestoredInitialState,
    images,
  ]);

  // 恢复背景颜色选中状态
  useEffect(() => {
    // 如果已经完成初始状态恢复，不再执行
    if (hasRestoredInitialState) {
      return;
    }

    // 如果用户已经手动选择了背景图片，不要恢复背景颜色
    if (currentBackgroundImageUrl !== undefined) {
      return;
    }

    // 如果当前颜色不是初始值，说明用户可能已经手动选择了颜色，不要恢复
    if (currentColor !== 'transparent') {
      return;
    }

    // 检查当前图片是否有backgroundColor需要恢复
    const firstImage = images[0];
    if (!firstImage) return;

    // 从store中获取图片的完整信息
    const imageFromStore = useImageStore.getState().images.get(firstImage.id);
    if (!imageFromStore?.backgroundColor) {
      // 没有背景颜色，标记为已恢复
      setHasRestoredInitialState(true);
      return;
    }

    // 如果图片有背景颜色且不是透明，恢复颜色选中状态
    if (imageFromStore.backgroundColor !== 'transparent') {
      setCurrentColor(imageFromStore.backgroundColor);
      setActiveTab('color');
      setHasRestoredInitialState(true);
    } else {
      // 背景颜色是透明，标记为已恢复
      setHasRestoredInitialState(true);
    }
  }, [
    images,
    currentBackgroundImageUrl,
    currentColor,
    hasRestoredInitialState,
  ]);

  const handleColorChange = useCallback(
    (newColor: string, fromCustomPicker = false) => {
      setCurrentColor(newColor);
      setIsCustomColorActive(fromCustomPicker);
      // 选择背景颜色时，清除背景图片（互斥逻辑）
      if (currentBackgroundImageUrl !== undefined) {
        setCurrentBackgroundImageUrl(undefined);
      }
    },
    [currentBackgroundImageUrl]
  );

  // 节流处理颜色变化，避免拖动时卡顿
  const throttledColorChange = useCallback(
    (newColor: string) => {
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }

      throttleTimeoutRef.current = setTimeout(() => {
        handleColorChange(newColor, true);
      }, 50); // 50ms 延迟
    },
    [handleColorChange]
  );

  const handleColorInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newColor = event.target.value;
    throttledColorChange(newColor);
  };

  const handleColorInputClick = () => {
    // 点击input时立即激活自定义颜色状态
    setIsCustomColorActive(true);
  };

  const handleApply = async () => {
    const settings: BackgroundSettings = {
      type: activeTab,
      color: activeTab === 'color' ? currentColor : undefined,
      backgroundImageUrl:
        activeTab === 'photos' ? currentBackgroundImageUrl : undefined,
      backgroundImageId:
        activeTab === 'photos' ? currentBackgroundImageId : undefined,
    };

    // 检查是否需要先进行去背操作
    // 当用户选择了任何背景颜色或背景图片时，如果图片还是原图状态，需要先去背
    const needsBackgroundRemoval =
      (activeTab === 'color' && currentColor !== undefined) ||
      (activeTab === 'photos' && currentBackgroundImageUrl !== undefined);

    if (needsBackgroundRemoval && onBatchRemoveBackground) {
      // 找出所有需要处理的图片（original状态且没有processedUrl的，排除locked状态）
      const imagesToProcess = images
        .filter(img => img.status === 'original' && !img.processedUrl)
        .map(img => img.id);

      if (imagesToProcess.length > 0) {
        try {
          await onBatchRemoveBackground(imagesToProcess);
        } catch (error) {
          console.error('自动去背处理失败:', error);
          return; // 如果背景去除失败，不继续应用设置
        }
      }
    }

    // 应用背景设置到所有图片（包括颜色背景和背景图片）
    if (onApply) {
      onApply(settings);
    }
  };

  return (
    <div className='w-[344px] bg-white border-r border-border-divider flex flex-col h-full'>
      {/* 标题区域 */}
      <div className='pt-6 px-4 mb-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>
            {batchEditor('interface.background')}
          </h2>
        </div>
      </div>

      {/* 标签切换 */}
      <div className='flex-1 overflow-hidden'>
        <Tabs
          value={activeTab}
          onValueChange={value => setActiveTab(value as 'color' | 'photos')}
        >
          <div className='px-4'>
            <TabsList className='w-full h-10 bg-border p-0.5'>
              <TabsTrigger
                value='color'
                className='data-[state=inactive]:text-[#878787] cursor-pointer'
              >
                {batchEditor('background.color')}
              </TabsTrigger>
              <TabsTrigger
                value='photos'
                className='data-[state=inactive]:text-[#878787] cursor-pointer'
              >
                {batchEditor('background.photos')}
              </TabsTrigger>
            </TabsList>
          </div>

          {/* 内容区域 - 可滚动 */}
          <div className='flex-1'>
            <TabsContent value='color' className='mt-2'>
              <div className='px-4 space-y-4'>
                <div className='space-y-2'>
                  <p className='text-[14px] font-medium text-[#878787]'>
                    {batchEditor('background.customColor')}
                  </p>
                  <div className='flex items-start justify-start relative'>
                    {isCustomColorActive ? (
                      <Image
                        src='/apps/icons/customColorActive.svg'
                        alt='customColorActive'
                        width={32}
                        height={32}
                      />
                    ) : (
                      <Image
                        src='/apps/icons/customColor.svg'
                        alt='customColor'
                        width={32}
                        height={32}
                      />
                    )}
                    <input
                      ref={colorInputRef}
                      type='color'
                      value={
                        currentColor === 'transparent'
                          ? '#FFFFFF'
                          : currentColor
                      }
                      onChange={handleColorInputChange}
                      onClick={handleColorInputClick}
                      style={{
                        display: 'block',
                        opacity: 0,
                        width: '32px',
                        height: '32px',
                        border: 'none',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                      }}
                    />
                  </div>
                </div>

                <div className='space-y-2'>
                  <p className='text-[14px] font-medium text-[#878787] '>
                    {/* TODO: 需要添加 "Preset Color" 的国际化文案 */}
                    Preset Color
                  </p>
                  <div className='grid grid-cols-8 gap-2'>
                    {PREDEFINED_COLORS.map(color => (
                      <Button
                        key={color}
                        variant='outline'
                        className={`w-8 h-8 p-0 rounded-full hover:scale-110 transition-transform ${
                          currentColor === color && !isCustomColorActive
                            ? 'ring-2 ring-offset-2 ring-[#FFCC03]'
                            : ''
                        } ${color === 'transparent' ? 'checkerboard' : ''}`}
                        style={{
                          backgroundColor:
                            color === 'transparent' ? undefined : color,
                          border: '1px solid rgba(18, 18, 18, 0.10)',
                        }}
                        onClick={() => handleColorChange(color, false)}
                        title={color === 'transparent' ? '透明' : color}
                      >
                        {color === 'transparent' && (
                          <div className='w-full h-full flex items-center justify-center rounded-full'></div>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value='photos' className='mt-2'>
              <BatchBackgroundImagePicker
                currentBackgroundImageUrl={currentBackgroundImageUrl}
                onSelectImage={url => {
                  setCurrentBackgroundImageUrl(url);

                  // 查找对应的背景图片ID
                  let backgroundImageId: string | undefined;
                  if (url) {
                    if (url.startsWith('blob:')) {
                      // 自定义上传的背景图片
                      const matchedBackground = uploadedBackgroundImages.find(
                        bg => bg.url === url
                      );
                      backgroundImageId = matchedBackground?.id;
                    } else if (url.startsWith('/apps/images/background/')) {
                      // 预设背景图片，生成固定ID
                      backgroundImageId = `preset-${url.split('/').pop()?.replace('.png', '')}`;
                    }
                  }
                  setCurrentBackgroundImageId(backgroundImageId);

                  // 选择背景图片时，将背景颜色设为透明（互斥逻辑）
                  if (url !== undefined && currentColor !== 'transparent') {
                    setCurrentColor('transparent');
                    setIsCustomColorActive(false);
                  }
                }}
                onFileUpload={onFileUpload || (() => {})}
                uploadedImages={
                  uploadedBackgroundImages.length > 0
                    ? uploadedBackgroundImages
                    : uploadedImages
                }
                onAddBackgroundImage={onAddBackgroundImage}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* 底部应用按钮 - 固定在底部 */}
      <div className='px-4 py-6'>
        <Button
          onClick={handleApply}
          disabled={shouldDisableBatchApplyButton(isProcessing, images)}
          className='w-full h-12 text-base font-medium'
          size='lg'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={16}
                height={16}
                className='animate-spin'
              />
              {common('processing')}
            </div>
          ) : (
            common('apply')
          )}
        </Button>
      </div>
    </div>
  );
}
