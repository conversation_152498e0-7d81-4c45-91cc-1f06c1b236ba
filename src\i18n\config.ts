export type Locale =
  | 'en'
  | 'zh'
  | 'zh-TW'
  | 'ja'
  | 'fr'
  | 'es'
  | 'it'
  | 'ar'
  | 'de'
  | 'ko'
  | 'pt'
  | 'ru'
  | 'th'
  | 'tr'
  | 'id';

export const defaultLocale: Locale = 'zh';

export const locales: readonly Locale[] = [
  'en',
  'zh',
  'zh-TW',
  'ja',
  'fr',
  'es',
  'it',
  'ar',
  'de',
  'ko',
  'pt',
  'ru',
  'th',
  'tr',
  'id',
] as const;

export const localeConfig = {
  en: {
    name: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
  },
  zh: {
    name: '简体中文',
    flag: '🇨🇳',
    dir: 'ltr',
  },
  'zh-TW': {
    name: '繁體中文',
    flag: '🇹🇼',
    dir: 'ltr',
  },
  ja: {
    name: '日本語',
    flag: '🇯🇵',
    dir: 'ltr',
  },
  fr: {
    name: 'Fran<PERSON>',
    flag: '🇫🇷',
    dir: 'ltr',
  },
  es: {
    name: 'Espa<PERSON><PERSON>',
    flag: '🇪🇸',
    dir: 'ltr',
  },
  it: {
    name: 'Italiano',
    flag: '🇮🇹',
    dir: 'ltr',
  },
  ar: {
    name: 'العربية',
    flag: '🇸🇦',
    dir: 'rtl',
  },
  de: {
    name: 'Deutsch',
    flag: '🇩🇪',
    dir: 'ltr',
  },
  ko: {
    name: '한국어',
    flag: '🇰🇷',
    dir: 'ltr',
  },
  pt: {
    name: 'Português',
    flag: '🇵🇹',
    dir: 'ltr',
  },
  ru: {
    name: 'Русский',
    flag: '🇷🇺',
    dir: 'ltr',
  },
  th: {
    name: 'ไทย',
    flag: '🇹🇭',
    dir: 'ltr',
  },
  tr: {
    name: 'Türkçe',
    flag: '🇹🇷',
    dir: 'ltr',
  },
  id: {
    name: 'Bahasa Indonesia',
    flag: '🇮🇩',
    dir: 'ltr',
  },
} as const;
