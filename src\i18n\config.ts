export type Locale = 'en' | 'zh' | 'zh-TW' | 'ja' | 'fr' | 'es' | 'it' | 'ar';

export const defaultLocale: Locale = 'zh';

export const locales: readonly Locale[] = [
  'en',
  'zh',
  'zh-TW',
  'ja',
  'fr',
  'es',
  'it',
  'ar',
] as const;

export const localeConfig = {
  en: {
    name: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
  },
  zh: {
    name: '简体中文',
    flag: '🇨🇳',
    dir: 'ltr',
  },
  'zh-TW': {
    name: '繁體中文',
    flag: '🇹🇼',
    dir: 'ltr',
  },
  ja: {
    name: '日本語',
    flag: '🇯🇵',
    dir: 'ltr',
  },
  fr: {
    name: 'Français',
    flag: '🇫🇷',
    dir: 'ltr',
  },
  es: {
    name: 'Español',
    flag: '🇪🇸',
    dir: 'ltr',
  },
  it: {
    name: 'Italiano',
    flag: '🇮🇹',
    dir: 'ltr',
  },
  ar: {
    name: 'العربية',
    flag: '🇸🇦',
    dir: 'rtl',
  },
} as const;
