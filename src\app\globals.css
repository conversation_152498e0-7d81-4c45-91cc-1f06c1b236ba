/* 导入 Tailwind CSS 的基础、组件和工具样式 */
@import 'tailwindcss';
/* 导入一个用于动画的 Tailwind CSS 插件 */
@import 'tw-animate-css';

/* 字体通过 Google Fonts 加载 */

/* 定义一个自定义的 'dark' 变体，当父元素有 .dark 类时生效 */
@custom-variant dark (&:is(.dark *));

/*
  定义内联主题，将 CSS 自定义属性（CSS 变量）映射到 Tailwind 的主题系统中。
  这允许我们在 Tailwind 的配置文件或任意 CSS 中使用这些变量。
*/
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geologica);
  --font-mono: var(--font-geist-mono);

  /* 品牌主题色系统 */
  --color-brand-primary: var(--brand-primary);
  --color-brand-primary-hover: var(--brand-primary-hover);
  --color-brand-primary-disabled: var(--brand-primary-disabled);
  --color-brand-orange: var(--brand-orange);
  --color-brand-red: var(--brand-red);
  --color-brand-green: var(--brand-green);

  /* 文字色系统 */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  --color-text-quaternary: var(--text-quaternary);
  --color-border-divider: var(--border-divider);
  --color-bg-light: var(--bg-light);

  /* 基础UI颜色 */
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
}

:root {
  /* 字体配置 - Geologica 通过 Google Fonts 加载 */
  --font-geologica:
    'Geologica', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
    'Helvetica Neue', Arial, sans-serif;
  --font-geist-mono:
    'Geist Mono', 'SF Mono', Monaco, 'Inconsolata', 'Fira Code',
    'Droid Sans Mono', 'Source Code Pro', monospace;

  /* 圆角配置 */
  --radius: 0.625rem;

  /* 品牌主题色系统 */
  --brand-primary: #ffcc03;
  --brand-primary-hover: #ffe343;
  --brand-primary-disabled: #fff7c7;
  --brand-orange: #ff781f;
  --brand-red: #ff4d4f;
  --brand-green: #52c41a;

  /* 文字色系统 */
  --text-primary: #121212; /* 黑 01 / 文字色 */
  --text-secondary: #878787; /* 灰 02 / 文字色 */
  --text-tertiary: #b8b8b8; /* 灰 03 / 文字色 */
  --text-quaternary: #57575f; /* 灰 04 */
  --border-divider: #e7e7e7; /* 灰 05 / 描边色&分割线 */
  --bg-light: #f9fafb; /* 灰 06 / 背景色 */

  /* 基础UI配置 - 浅色主题 */
  --background: #ffffff;
  --foreground: var(--text-primary);
  --card: #ffffff;
  --card-foreground: var(--text-primary);
  --popover: #ffffff;
  --popover-foreground: var(--text-primary);
  --primary: var(--brand-primary);
  --primary-foreground: #ffffff;
  --secondary: var(--bg-light);
  --secondary-foreground: var(--text-primary);
  --muted: var(--bg-light);
  --muted-foreground: var(--text-secondary);
  --accent: var(--bg-light);
  --accent-foreground: var(--text-primary);
  --destructive: var(--brand-red);
  --border: var(--border-divider);
  --input: var(--border-divider);
  --ring: var(--brand-primary);
}

.dark {
  /* 深色主题配置 */
  --background: #0a0a0a;
  --foreground: #fafafa;
  --card: #121212;
  --card-foreground: #fafafa;
  --popover: #121212;
  --popover-foreground: #fafafa;
  --primary: var(--brand-primary);
  --primary-foreground: #ffffff;
  --secondary: #1a1a1a;
  --secondary-foreground: #fafafa;
  --muted: #1a1a1a;
  --muted-foreground: #a1a1aa;
  --accent: #1a1a1a;
  --accent-foreground: #fafafa;
  --destructive: var(--brand-red);
  --border: #27272a;
  --input: #27272a;
  --ring: var(--brand-primary);

  /* 深色主题下的文字色调整 */
  --text-primary: #fafafa;
  --text-secondary: #a1a1aa;
  --text-tertiary: #71717a;
  --text-quaternary: #52525b;
  --border-divider: #27272a;
  --bg-light: #1a1a1a;
}

@layer base {
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geologica);
  }
}

html,
body {
  height: 100%;
}

img {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  user-select: none; /* Safari */
}

/* ================================= */
/* 通用图标交互系统                     */
/* ================================= */

.icon-interactive {
  stroke: var(--text-primary);
  transition: stroke 0.2s ease-in-out;
}

/* 删除按钮的白色图标样式 */
.icon-interactive-white {
  stroke: white;
  transition: stroke 0.2s ease-in-out;
}

/* 支持多种hover触发方式 */
.icon-interactive:hover,
.icon-interactive.active,
.icon-interactive:focus,
.icon-interactive-white:hover,
.icon-interactive-white.active,
.icon-interactive-white:focus,
.interactive-container:hover .icon-interactive-white,
.interactive-container:hover .icon-interactive {
  stroke: var(--brand-primary);
}

/* 禁用状态 */
.icon-interactive:disabled,
.icon-interactive.disabled,
.icon-interactive-white:disabled,
.icon-interactive-white.disabled {
  /* *.disabled .icon-interactive  留着吧，可能有用呢？ */
  /* *:disabled .icon-interactive, */
  stroke: var(--brand-primary-disabled);
}

/* ================================= */
/* 其他全局样式                       */
/* ================================= */

/* 闪烁动画效果 */
@keyframes sparkle-effect {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: scale(1.2) rotate(30deg);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.8) rotate(-20deg);
  }
  75% {
    opacity: 1;
    transform: scale(1.1) rotate(15deg);
  }
}

/* 自定义 3px 边框样式 */
.border-3 {
  border-width: 3px;
}

/* 透明背景棋盘格样式 */
.checkerboard {
  background-image:
    linear-gradient(45deg, #e7e7e7 25%, transparent 25%),
    linear-gradient(-45deg, #e7e7e7 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #e7e7e7 75%),
    linear-gradient(-45deg, transparent 75%, #e7e7e7 75%);
  background-size: 8px 8px;
  background-position:
    0 0,
    0 4px,
    4px -4px,
    -4px 0px;
  background-color: white;
}
