'use client';

import { useLocale } from 'next-intl';
import { useState, useTransition } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { Locale, localeConfig } from '@/i18n/config';
import { setUserLocale } from '@/services/locale';

// 支持的语言配置
const locales = Object.entries(localeConfig).map(([code, config]) => ({
  code: code as Locale,
  name: config.name,
  flag: config.flag,
}));

interface LanguageSwitcherProps {
  variant?: 'select' | 'button';
  className?: string;
}

export function LanguageSwitcher({
  variant = 'select',
  className = '',
}: LanguageSwitcherProps) {
  const locale = useLocale() as Locale;
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);

  const currentLocale = locales.find(l => l.code === locale) || locales[0];

  const handleLanguageChange = (newLocale: Locale) => {
    if (newLocale === locale) return;

    startTransition(() => {
      // 使用服务端函数设置语言
      setUserLocale(newLocale);
    });

    setIsOpen(false);
  };

  if (variant === 'select') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          disabled={isPending}
          className='flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50'
        >
          <span>{currentLocale.flag}</span>
          <span>{currentLocale.name}</span>
          <ChevronDown
            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {isOpen && (
          <div className='absolute top-full left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50'>
            {locales.map(loc => (
              <button
                key={loc.code}
                onClick={() => handleLanguageChange(loc.code)}
                disabled={isPending}
                className={`w-full flex items-center justify-between px-3 py-2 text-sm text-left hover:bg-gray-50 disabled:opacity-50 transition-colors ${
                  loc.code === locale
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-700'
                }`}
              >
                <div className='flex items-center gap-2'>
                  <span>{loc.flag}</span>
                  <span>{loc.name}</span>
                </div>
                {loc.code === locale && (
                  <Check className='h-4 w-4 text-blue-600' />
                )}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Button variant
  return (
    <div className={`flex gap-2 ${className}`}>
      {locales.map(loc => (
        <button
          key={loc.code}
          onClick={() => handleLanguageChange(loc.code)}
          disabled={isPending}
          className={`px-3 py-1 text-sm rounded-md transition-colors disabled:opacity-50 ${
            loc.code === locale
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          {loc.flag} {loc.name}
        </button>
      ))}
    </div>
  );
}
